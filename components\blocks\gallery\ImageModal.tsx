"use client";

import { ImageData, GalleryTranslations } from "@/types/blocks/gallery";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";


interface ImageModalProps {
  image: ImageData | null;
  isOpen: boolean;
  onClose: () => void;
  translations?: GalleryTranslations;
}

export default function ImageModal({ image, isOpen, onClose, translations }: ImageModalProps) {
  if (!image) return null;

  const renderDetailItem = (label: string, value: string | undefined) => {
    if (!value) return null;
    
    return (
      <div className="space-y-2">
        <h4 className="text-sm font-medium text-muted-foreground">{label}</h4>
        <p className="text-sm leading-relaxed">{value}</p>
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] p-0 overflow-hidden">
        <div className="grid grid-cols-1 lg:grid-cols-2 h-full">
          {/* 图片区域 */}
          <div className="relative bg-black/5 flex items-center justify-center p-4">
            <img
              src={image.src}
              alt={image.alt}
              className="max-w-full max-h-full object-contain rounded-lg shadow-lg"
              style={{
                aspectRatio: image.aspectRatio,
              }}
            />
          </div>

          {/* 详情区域 */}
          <div className="flex flex-col h-full">
            <DialogHeader className="p-6 pb-4">
              <DialogTitle className="text-xl font-semibold">
                {translations?.image_details || "图片详情"}
              </DialogTitle>
            </DialogHeader>

            <Separator />

            <div className="flex-1 p-6 overflow-y-auto">
              <div className="space-y-6">
                {/* 风格参数 */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">生成参数</h3>
                  
                  {renderDetailItem(
                    translations?.style || "风格",
                    image.style
                  )}
                  
                  {renderDetailItem(
                    translations?.color || "颜色",
                    image.color
                  )}
                  
                  {renderDetailItem(
                    translations?.lighting || "光影",
                    image.lighting
                  )}
                  
                  {renderDetailItem(
                    translations?.composition || "构图",
                    image.composition
                  )}
                </div>

                {/* 提示词 */}
                {image.prompt && (
                  <>
                    <Separator />
                    <div className="space-y-3">
                      <h3 className="text-lg font-medium">
                        {translations?.prompt || "提示词"}
                      </h3>
                      <div className="bg-muted/50 rounded-lg p-4">
                        <p className="text-sm leading-relaxed whitespace-pre-wrap">
                          {image.prompt}
                        </p>
                      </div>
                    </div>
                  </>
                )}

                {/* 标签 */}
                {image.tags && image.tags.length > 0 && (
                  <>
                    <Separator />
                    <div className="space-y-3">
                      <h3 className="text-lg font-medium">标签</h3>
                      <div className="flex flex-wrap gap-2">
                        {image.tags.map((tag, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
