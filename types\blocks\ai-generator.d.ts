export interface AIGeneratorSettings {
  model?: string;
  style?: string;
  size?: string;
  quality?: string;
  steps?: number;
}

export interface StyleOption {
  zh: string;
  en: string;
  reference: string;
  icon: string;
}

export interface ColorOption {
  zh: string;
  en: string;
  reference: string;
  icon: string;
}

export interface LightingOption {
  zh: string;
  en: string;
  reference: string;
  icon: string;
}

export interface CompositionOption {
  zh: string;
  en: string;
  reference: string;
  icon: string;
}

export interface AIGeneratorTranslations {
  tabs?: {
    text_to_image?: string;
    image_to_image?: string;
  };
  placeholders?: {
    negative_prompt?: string;
    image_modification?: string;
    file_support?: string;
  };
  settings?: {
    ratio?: string;
    style?: string;
    color?: string;
    lighting?: string;
    composition?: string;
  };
  options?: {
    style?: StyleOption[];
    color?: string[];
    lighting?: string[];
    composition?: string[];
  };
  controls?: {
    high_quality?: string;
    generating?: string;
    generate?: string;
  };
}

export interface AIGenerator {
  disabled?: boolean;
  name?: string;
  title?: string;
  description?: string;
  text_to_image?: {
    title?: string;
    placeholder?: string;
    settings?: AIGeneratorSettings;
  };
  image_to_image?: {
    title?: string;
    upload_text?: string;
    settings?: AIGeneratorSettings;
  };
  translations?: AIGeneratorTranslations;
}
