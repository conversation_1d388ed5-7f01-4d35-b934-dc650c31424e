"use client";

import { useState, useEffect, useMemo } from "react";
import { GallerySection, ImageData } from "@/types/blocks/gallery";
import ImageCard from "./ImageCard";
import ImageModal from "./ImageModal";
import { cn } from "@/lib/utils";

interface GalleryProps {
  gallery: GallerySection;
  className?: string;
}

// 模拟图片数据 - 使用现有的 showcases 图片作为占位符
const mockImages: ImageData[] = [
  {
    id: "1",
    src: "/imgs/showcases/1.png",
    alt: "AI Generated Portrait 1",
    width: 512,
    height: 768,
    aspectRatio: 512 / 768,
    style: "Anime Style",
    color: "Vibrant Colors",
    lighting: "Soft Lighting",
    composition: "Portrait",
    prompt: "A beautiful anime character with vibrant colors, soft lighting, portrait composition",
    tags: ["anime", "portrait", "vibrant", "soft lighting"]
  },
  {
    id: "2",
    src: "/imgs/showcases/2.png",
    alt: "AI Generated Landscape 1",
    width: 512,
    height: 640,
    aspectRatio: 512 / 640,
    style: "Realistic Style",
    color: "Natural Colors",
    lighting: "Golden Hour",
    composition: "Landscape",
    prompt: "A stunning landscape with natural colors during golden hour",
    tags: ["landscape", "realistic", "golden hour", "nature"]
  },
  {
    id: "3",
    src: "/imgs/showcases/3.png",
    alt: "AI Generated Character 1",
    width: 512,
    height: 720,
    aspectRatio: 512 / 720,
    style: "Cartoon Style",
    color: "Pastel Tones",
    lighting: "Ambient Light",
    composition: "Full Body",
    prompt: "A cute cartoon character with pastel tones and ambient lighting",
    tags: ["cartoon", "character", "pastel", "cute"]
  },
  {
    id: "4",
    src: "/imgs/showcases/4.png",
    alt: "AI Generated Art 1",
    width: 512,
    height: 800,
    aspectRatio: 512 / 800,
    style: "Abstract Style",
    color: "Dark Palette",
    lighting: "Dramatic Light",
    composition: "Abstract",
    prompt: "An abstract artwork with dark palette and dramatic lighting",
    tags: ["abstract", "dark", "dramatic", "artistic"]
  },
  {
    id: "5",
    src: "/imgs/showcases/5.png",
    alt: "AI Generated Fantasy 1",
    width: 512,
    height: 680,
    aspectRatio: 512 / 680,
    style: "Fantasy Style",
    color: "Magical Colors",
    lighting: "Mystical Light",
    composition: "Fantasy Scene",
    prompt: "A magical fantasy scene with mystical lighting and enchanting colors",
    tags: ["fantasy", "magical", "mystical", "enchanting"]
  },
  {
    id: "6",
    src: "/imgs/showcases/6.png",
    alt: "AI Generated Sci-Fi 1",
    width: 512,
    height: 750,
    aspectRatio: 512 / 750,
    style: "Sci-Fi Style",
    color: "Neon Colors",
    lighting: "Futuristic Light",
    composition: "Sci-Fi Scene",
    prompt: "A futuristic sci-fi scene with neon colors and advanced lighting",
    tags: ["sci-fi", "futuristic", "neon", "advanced"]
  }
];

export default function Gallery({ gallery, className }: GalleryProps) {
  const [selectedImage, setSelectedImage] = useState<ImageData | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [images, setImages] = useState<ImageData[]>([]);

  // 使用传入的图片数据或模拟数据
  useEffect(() => {
    if (gallery.images && gallery.images.length > 0) {
      setImages(gallery.images);
    } else {
      // 使用模拟数据，生成更多图片用于演示
      const extendedMockImages = Array.from({ length: 24 }, (_, index) => ({
        ...mockImages[index % mockImages.length],
        id: `${index + 1}`,
        src: `/imgs/showcases/${(index % 9) + 1}.png`,
      }));
      setImages(extendedMockImages);
    }
  }, [gallery.images]);

  // 瀑布流布局计算
  const columns = useMemo(() => {
    const columnCount = {
      mobile: 2,
      tablet: 3,
      desktop: 4
    };

    // 为每列创建数组
    const cols: ImageData[][] = Array.from({ length: columnCount.desktop }, () => []);
    const colHeights = Array(columnCount.desktop).fill(0);

    // 将图片分配到高度最小的列
    images.forEach((image) => {
      const shortestColIndex = colHeights.indexOf(Math.min(...colHeights));
      cols[shortestColIndex].push(image);
      colHeights[shortestColIndex] += image.aspectRatio ? (1 / image.aspectRatio) : 1;
    });

    return cols;
  }, [images]);

  const handleImageClick = (image: ImageData) => {
    setSelectedImage(image);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setTimeout(() => setSelectedImage(null), 300);
  };

  if (images.length === 0) {
    return (
      <section className={cn("py-16", className)}>
        <div className="container mx-auto px-4">
          <div className="text-center text-muted-foreground">
            <p>{gallery.translations?.no_data || "暂无图片"}</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className={cn("py-16", className)}>
      <div className="container mx-auto px-4">
        {/* 标题区域 */}
        {(gallery.title || gallery.subtitle) && (
          <div className="text-center mb-12">
            {gallery.title && (
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                {gallery.title}
              </h2>
            )}
            {gallery.subtitle && (
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                {gallery.subtitle}
              </p>
            )}
          </div>
        )}

        {/* 瀑布流网格 */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {columns.map((column, columnIndex) => (
            <div key={columnIndex} className="flex flex-col gap-4">
              {column.map((image) => (
                <ImageCard
                  key={image.id}
                  image={image}
                  onClick={() => handleImageClick(image)}
                  className="w-full"
                />
              ))}
            </div>
          ))}
        </div>

        {/* 图片详情模态框 */}
        <ImageModal
          image={selectedImage}
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          translations={gallery.translations}
        />
      </div>
    </section>
  );
}
