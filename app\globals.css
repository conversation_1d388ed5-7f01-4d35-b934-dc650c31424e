@tailwind base;
@tailwind components;
@tailwind utilities;

@import "theme.css";

html {
  scroll-behavior: smooth;
  /* 防止下拉菜单打开时滚动条隐藏导致的偏移 */
  scrollbar-gutter: stable;
}

/* 确保背景视频覆盖整个视口包括滚动条 */
body {
  overflow-x: hidden;
}

/* 为页面根元素设置背景图片，避免视频加载时的白色闪烁 */
html {
  background: url('/imgs/background/background-poster.webp') center center / cover no-repeat fixed;
}

/* 隐藏滚动条但保持滚动功能 */
::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

/* 对于Firefox */
html {
  scrollbar-width: none;
}

/* 对于IE和Edge */
body {
  -ms-overflow-style: none;
}

/* 动画关键帧 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}



/* 备选方案：自定义滚动条样式（如需要可取消注释）
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}


*/

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply text-foreground;
    background: transparent;
  }

  :root {
    --sidebar-background: var(--background);
    --sidebar-foreground: var(--foreground);
    --sidebar-primary: var(--primary);
    --sidebar-primary-foreground: var(--primary-foreground);
    --sidebar-accent: var(--background);
    --sidebar-accent-foreground: var(--primary);
    --sidebar-border: var(--border);
    --sidebar-ring: var(--ring);
  }

}
