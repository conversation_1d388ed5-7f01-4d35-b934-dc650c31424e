{"template": "shipany-template-one", "theme": "light", "header": {"brand": {"title": "ShipAny", "logo": {"src": "/logo.png", "alt": "ShipAny"}, "url": "/"}, "nav": {"items": [{"title": "Features", "url": "/#feature", "icon": "HiOutlineSparkles"}, {"title": "Pricing", "url": "/#pricing", "icon": "MdPayment"}]}, "buttons": [], "show_sign": true, "show_theme": true, "show_locale": true}, "hero": {"title": "Ship Any AI Startups in hours, not days", "highlight_text": "Ship Any", "description": "ShipAny is a NextJS boilerplate for building AI SaaS startups.<br/>Ship Fast with a variety of templates and components.", "show_happy_users": false, "show_badge": false}, "ai_generator": {"name": "ai-generator", "text_to_image": {"title": "Text to Image", "placeholder": "Describe the image you want to generate..."}, "image_to_image": {"title": "Image to Image", "upload_text": "Click to upload image"}, "translations": {"tabs": {"text_to_image": "Text to Image", "image_to_image": "Image to Image"}, "placeholders": {"negative_prompt": "Describe what you don't want in the image...", "image_modification": "Describe the modifications you want to make to the image...", "file_support": "Supports JPG, PNG, WebP"}, "settings": {"ratio": "<PERSON><PERSON>", "style": "Style", "color": "Color", "lighting": "Lighting", "composition": "Composition"}, "options": {"style": [{"zh": "无风格", "en": "No Style", "reference": "", "icon": "Ban"}, {"zh": "写实风", "en": "Semi-Realistic", "reference": "<PERSON><PERSON> (Netflix)", "icon": "Camera"}, {"zh": "极简扁平", "en": "Flat Minimal", "reference": "Adventure Time", "icon": "Square"}, {"zh": "超级英雄风", "en": "Comic Book", "reference": "Batman: The Animated Series", "icon": "Zap"}, {"zh": "青年热血动漫风", "en": "Shounen Style", "reference": "My Hero Academia", "icon": "Flame"}, {"zh": "可爱风", "en": "Moe Style", "reference": "K-On!", "icon": "Heart"}, {"zh": "暗黑成人风", "en": "Gritty Style", "reference": "Castlevania", "icon": "Moon"}, {"zh": "哥特风", "en": "Gothic Style", "reference": "Billy & Mandy", "icon": "Crown"}, {"zh": "脱线搞怪风", "en": "Absurdist", "reference": "<PERSON> and <PERSON><PERSON><PERSON>", "icon": "Smile"}, {"zh": "手绘感风", "en": "Sketchy Lines", "reference": "The Midnight Gospel", "icon": "Pencil"}, {"zh": "二头身Q版", "en": "Chibi Style", "reference": "Lucky Star", "icon": "Circle"}, {"zh": "超现实风", "en": "Surreal Style", "reference": "FLCL", "icon": "<PERSON><PERSON><PERSON>"}, {"zh": "黑白漫画风", "en": "Monochrome", "reference": "Samurai Jack", "icon": "Contrast"}, {"zh": "美式卡通", "en": "Classic Western", "reference": "<PERSON> and <PERSON>", "icon": "Star"}, {"zh": "高饱和亮色", "en": "Vibrant Colors", "reference": "<PERSON><PERSON><PERSON>", "icon": "Rainbow"}, {"zh": "2.5D分层", "en": "Cutout Style", "reference": "South Park", "icon": "Layers"}, {"zh": "青春柔光风", "en": "Soft Pastel", "reference": "Your Name", "icon": "Sun"}, {"zh": "抽象表现风", "en": "Experimental", "reference": "Pup<PERSON>", "icon": "<PERSON><PERSON><PERSON>"}, {"zh": "动态动感风", "en": "Kinetic Style", "reference": "Attack on Titan", "icon": "Wind"}, {"zh": "视觉小说风", "en": "Visual Novel", "reference": "Steins;Gate", "icon": "Book"}, {"zh": "复古赛璐璐手绘风", "en": "<PERSON><PERSON>", "reference": "<PERSON>", "icon": "Clock"}], "color": [{"zh": "无色彩", "en": "No Color", "reference": "", "icon": "Ban"}, {"zh": "高饱和鲜艳色", "en": "Vibrant Colors", "reference": "Kill la Kill", "icon": "Palette"}, {"zh": "柔和淡色系", "en": "<PERSON><PERSON>", "reference": "Your Name", "icon": "Flower"}, {"zh": "低饱和色", "en": "Muted Colors", "reference": "Death Note", "icon": "Moon"}, {"zh": "暗黑风格色调", "en": "Dark Palette", "reference": "Tokyo Ghoul", "icon": "Skull"}, {"zh": "复古色彩", "en": "Retro Colors", "reference": "<PERSON>", "icon": "Clock"}, {"zh": "冷色主导", "en": "<PERSON>", "reference": "Serial Experiments Lain", "icon": "Snowflake"}, {"zh": "暖色主导", "en": "<PERSON>nes", "reference": "Clannad", "icon": "Sun"}, {"zh": "单色调", "en": "Monochrome", "reference": "Samurai Jack", "icon": "Circle"}, {"zh": "高对比强明暗", "en": "High Contrast", "reference": "Attack on Titan", "icon": "Contrast"}, {"zh": "赛博霓虹色", "en": "Cyber Neon", "reference": "<PERSON>", "icon": "Zap"}, {"zh": "梦幻透明感色系", "en": "<PERSON><PERSON>", "reference": "The Garden of Words", "icon": "<PERSON><PERSON><PERSON>"}, {"zh": "自然写实色调", "en": "Natural Colors", "reference": "Spirited Away", "icon": "Mountain"}], "lighting": [{"zh": "无光影", "en": "No Light", "reference": "", "icon": "Ban"}, {"zh": "柔光", "en": "Soft Lighting", "reference": "Your Name", "icon": "Sun"}, {"zh": "高对比光", "en": "High Contrast Lighting", "reference": "Batman: <PERSON><PERSON>", "icon": "Contrast"}, {"zh": "逆光", "en": "Backlighting", "reference": "<PERSON>", "icon": "Sunrise"}, {"zh": "顶光", "en": "Top Lighting", "reference": "Death Note", "icon": "ArrowUp"}, {"zh": "底光", "en": "Under Lighting", "reference": "Mononoke", "icon": "ArrowDown"}, {"zh": "轮廓光", "en": "<PERSON><PERSON>", "reference": "Fate/stay night: UBW", "icon": "Circle"}, {"zh": "环境光", "en": "Ambient Lighting", "reference": "Spirited Away", "icon": "Globe"}, {"zh": "聚焦高亮", "en": "Spot Lighting", "reference": "<PERSON>b Psycho 100", "icon": "Flashlight"}, {"zh": "光斑", "en": "Light Leaks / Lens Flare", "reference": "The Garden of Words", "icon": "<PERSON><PERSON><PERSON>"}, {"zh": "赛博霓虹光", "en": "Neon Lighting", "reference": "Cyberpunk: Edgerunners", "icon": "Zap"}, {"zh": "黄金时段光", "en": "Golden Hour Lighting", "reference": "5 Centimeters per Second", "icon": "Sunset"}, {"zh": "月光", "en": "Moonlight / Cool Light", "reference": "Princess <PERSON><PERSON><PERSON>", "icon": "Moon"}], "composition": [{"zh": "无构图", "en": "No Composition", "reference": "", "icon": "Ban"}, {"zh": "三分法", "en": "Rule of Thirds", "reference": "Spirited Away", "icon": "Grid3X3"}, {"zh": "中心构图", "en": "Centered Composition", "reference": "Demon Slayer", "icon": "Target"}, {"zh": "对称构图", "en": "Symmetrical Composition", "reference": "<PERSON>", "icon": "FlipHorizontal"}, {"zh": "动态构图", "en": "Dynamic Composition", "reference": "Promare", "icon": "Wind"}, {"zh": "引导线构图", "en": "Leading Lines", "reference": "<PERSON>", "icon": "ArrowRight"}, {"zh": "负空间构图", "en": "Negative Space", "reference": "5 Centimeters per Second", "icon": "Square"}, {"zh": "框架内构图", "en": "Frame Within a Frame", "reference": "Paranoia Agent", "icon": "<PERSON>ame"}, {"zh": "鸟瞰角度", "en": "Bird's Eye View", "reference": "Attack on Titan", "icon": "Eye"}, {"zh": "低角度视角", "en": "Low Angle Shot", "reference": "Code Geass", "icon": "ArrowUp"}, {"zh": "第一人称视角", "en": "POV Composition", "reference": "Re:Zero", "icon": "User"}, {"zh": "极端特写", "en": "Extreme Close-Up", "reference": "Death Note", "icon": "ZoomIn"}, {"zh": "对角线构图", "en": "Diagonal Composition", "reference": "FLCL", "icon": "Slash"}, {"zh": "黄金比例构图", "en": "Golden Ratio Composition", "reference": "Princess <PERSON><PERSON><PERSON>", "icon": "RotateCcw"}, {"zh": "镜像构图", "en": "Reflection Composition", "reference": "<PERSON><PERSON><PERSON>", "icon": "FlipVertical"}]}, "controls": {"high_quality": "High Quality", "generating": "Generating...", "generate": "Generate Image"}}}, "gallery": {"title": "AI Generated Gallery", "subtitle": "Explore amazing AI-generated artworks created with our platform", "translations": {"title": "AI Generated Gallery", "subtitle": "Explore amazing AI-generated artworks", "loading": "Loading...", "error": "Failed to load image", "retry": "Retry", "close": "Close", "image_details": "Image Details", "style": "Style", "color": "Color", "lighting": "Lighting", "composition": "Composition", "prompt": "Prompt", "no_data": "No images available"}}, "introduce": {"name": "introduce", "title": "What is ShipAny", "label": "Introduce", "description": "ShipAny is a NextJS boilerplate for building AI SaaS startups. Built in a variety of templates and components.", "image": {"src": "/imgs/features/1.png"}, "items": [{"title": "Ready-to-use Templates", "description": "Choose from dozens of production-ready AI SaaS templates to quickly start your project.", "icon": "RiNextjsFill"}, {"title": "Infrastructure Setup", "description": "Get instant access to scalable infrastructure with best practices built-in.", "icon": "RiDatabase2Line"}, {"title": "Quick Deployment", "description": "Deploy your AI SaaS application to production in hours, not days.", "icon": "RiCloudyFill"}]}, "benefit": {"name": "benefit", "title": "Why Choose ShipAny", "label": "Benefits", "description": "Get everything you need to launch your AI startup - from ready-to-use templates to technical support.", "items": [{"title": "Complete Framework", "description": "Built on Next.js with authentication, payments, and AI integration - everything works out of the box.", "icon": "RiNextjsFill", "image": {"src": "/imgs/features/2.png"}}, {"title": "Rich Templates Library", "description": "Choose from various AI SaaS templates to kickstart your project - chatbots, image generation, and more.", "icon": "RiClapperboardAiLine", "image": {"src": "/imgs/features/3.png"}}, {"title": "Technical Guidance", "description": "Get dedicated support and join our developer community to ensure your successful launch.", "icon": "RiCodeFill", "image": {"src": "/imgs/features/4.png"}}]}, "usage": {"name": "usage", "title": "How to Launch with ShipAny", "description": "Get your AI SaaS startup running in three simple steps:", "image": {"src": "/imgs/features/1.png"}, "image_position": "left", "text_align": "center", "items": [{"title": "Get ShipAny", "description": "Buy ShipAny with a one-time payment. Check your email for the code and documentation.", "image": {"src": "/imgs/features/5.png"}}, {"title": "Start Your Project", "description": "Read the documentation and clone the code of ShipAny. Start building your AI SaaS startup.", "image": {"src": "/imgs/features/6.png"}}, {"title": "Customize Your Project", "description": "Modify the template with your data and contents. Specific AI functionality needs.", "image": {"src": "/imgs/features/7.png"}}, {"title": "Deploy to Production", "description": "Deploy your project to production with a few steps and start serving customers immediately.", "image": {"src": "/imgs/features/8.png"}}]}, "feature": {"name": "feature", "title": "Key Features of ShipAny", "description": "Everything you need to launch your AI SaaS startup quickly and efficiently.", "items": [{"title": "Next.js Boilerplate", "description": "Production-ready Next.js templates with SEO-friendly structure and i18n support.", "icon": "RiNextjsFill"}, {"title": "Authentication & Payments", "description": "Integrated Google OAuth, one-tap login, and Stripe payment processing.", "icon": "RiKey2Fill"}, {"title": "Data Infrastructure", "description": "Built-in Supabase integration for reliable and scalable data storage.", "icon": "RiDatabase2Line"}, {"title": "One-Click Deployment", "description": "Seamless deployment to Vercel or Cloudflare with automated setup.", "icon": "RiCloudy2Fill"}, {"title": "Business Analytics", "description": "Integrated Google Analytics and Search Console for tracking growth.", "icon": "RiBarChart2Line"}, {"title": "AI-Ready Infrastructure", "description": "Pre-configured AI integration with built-in credits system and API sales.", "icon": "RiRobot2Line"}]}, "showcase": {"name": "showcase", "title": "AI SaaS Startups built with ShipAny", "description": "Easy to use and fast to ship.", "items": [{"title": "ThinkAny", "description": "AI Search Engine", "url": "https://thinkany.ai", "target": "_blank", "image": {"src": "/imgs/showcases/7.png"}}, {"title": "HeyBeauty", "description": "AI Virtual Try On", "url": "https://heybeauty.ai", "target": "_blank", "image": {"src": "/imgs/showcases/5.png"}}, {"title": "AI Wallpaper", "description": "AI Wallpaper Generator", "url": "https://aiwallpaper.shop", "target": "_blank", "image": {"src": "/imgs/showcases/1.png"}}, {"title": "AI Cover", "description": "AI Cover Generator", "url": "https://aicover.design", "target": "_blank", "image": {"src": "/imgs/showcases/2.png"}}, {"title": "GPTs Works", "description": "GPTs Directory", "url": "https://gpts.works", "target": "_blank", "image": {"src": "/imgs/showcases/3.png"}}, {"title": "Melod<PERSON>", "description": "AI Music Player", "url": "https://melodis.co", "target": "_blank", "image": {"src": "/imgs/showcases/4.png"}}, {"title": "<PERSON><PERSON>", "description": "AI Landing Page Generator", "url": "https://pagen.so", "target": "_blank", "image": {"src": "/imgs/showcases/6.png"}}, {"title": "SoraFM", "description": "AI Video Generator", "url": "https://sorafm.trys.ai", "target": "_blank", "image": {"src": "/imgs/showcases/8.png"}}, {"title": "PodLM", "description": "AI Podcast Generator", "url": "https://podlm.ai", "target": "_blank", "image": {"src": "/imgs/showcases/9.png"}}]}, "stats": {"name": "stats", "label": "Stats", "title": "People Love ShipAny", "description": "for it's easy to use and fast to ship.", "icon": "FaRegHeart", "items": [{"title": "Trusted by", "label": "99+", "description": "Customers"}, {"title": "Built in", "label": "20+", "description": "Components"}, {"title": "Ship Fast in", "label": "5", "description": "Minutes"}]}, "pricing": {"name": "pricing", "label": "Pricing", "title": "Pricing", "description": "Get all features of ShipAny, Ship your AI SaaS startups fast.", "groups": [], "items": [{"title": "Starter", "description": "Get started with your first SaaS startup.", "features_title": "Includes", "features": ["100 credits, valid for 1 month", "NextJS boilerplate", "SEO-friendly structure", "Payment with Stripe", "Data storage with Supabase", "Google Oauth & One-Tap Login", "i18n support"], "interval": "one-time", "amount": 9900, "cn_amount": 69900, "currency": "USD", "price": "$99", "original_price": "$199", "unit": "USD", "is_featured": false, "tip": "Pay once. Build unlimited projects!", "button": {"title": "Get ShipAny", "url": "/#pricing", "icon": "RiFlashlightFill"}, "product_id": "starter", "product_name": "ShipAny <PERSON>ilerplate Starter", "credits": 100, "valid_months": 1}, {"title": "Standard", "description": "Ship Fast with your SaaS Startups.", "label": "Popular", "features_title": "Everything in Starter, plus", "features": ["200 credits, valid for 3 month", "Deploy with Vercel or Cloudflare", "Generation of Privacy & Terms", "Google Analytics Integration", "Google Search Console Integration", "Discord community", "Technical support for your first ship", "Lifetime updates"], "interval": "one-time", "amount": 19900, "cn_amount": 139900, "currency": "USD", "price": "$199", "original_price": "$299", "unit": "USD", "is_featured": true, "tip": "Pay once. Build unlimited projects!", "button": {"title": "Get ShipAny", "url": "/#pricing", "icon": "RiFlashlightFill"}, "product_id": "standard", "product_name": "ShipAny Boilerplate Standard", "credits": 200, "valid_months": 3}, {"title": "Premium", "description": "Ship Any AI SaaS Startups.", "features_title": "Everything in Standard, plus", "features": ["300 credits, valid for 1 year", "Business Functions with AI", "User Center", "Credits System", "API Sales for your SaaS", "Admin System", "Priority Technical Support"], "interval": "one-time", "amount": 29900, "cn_amount": 199900, "currency": "USD", "price": "$299", "original_price": "$399", "unit": "USD", "is_featured": false, "tip": "Pay once. Build unlimited projects!", "button": {"title": "Get ShipAny", "url": "/#pricing", "icon": "RiFlashlightFill"}, "product_id": "premium", "product_name": "ShipAny Boilerplate Premium", "credits": 300, "valid_months": 12}]}, "testimonial": {"name": "testimonial", "label": "Testimonial", "title": "What Users Say About ShipAny", "description": "Hear from developers and founders who launched their AI startups with ShipAny.", "icon": "GoThumbsup", "items": [{"title": "<PERSON>", "label": "Founder of AIWallpaper.shop", "description": "ShipAny saved us months of development time. We launched our AI wallpaper business in just 2 days and got our first paying customer within a week!", "image": {"src": "/imgs/users/1.png"}}, {"title": "<PERSON>", "label": "CTO at HeyBeauty.ai", "description": "The pre-built AI infrastructure is a game-changer. We didn't have to worry about architecture - just focused on our AI beauty tech and went live fast.", "image": {"src": "/imgs/users/2.png"}}, {"title": "<PERSON>", "label": "Solo Developer", "description": "As a solo developer, <PERSON><PERSON><PERSON> gave me everything I needed - auth, payments, AI integration, and beautiful UI. Launched my SaaS in a weekend!", "image": {"src": "/imgs/users/3.png"}}, {"title": "<PERSON> Garcia", "label": "CEO of Melodisco", "description": "The templates are production-ready and highly customizable. We built our AI music platform in hours instead of months. Incredible time-to-market!", "image": {"src": "/imgs/users/4.png"}}, {"title": "<PERSON>", "label": "Tech Lead at GPTs.works", "description": "ShipAny's infrastructure is rock-solid. We scaled from 0 to 10k users without touching the backend. Best investment for our AI startup.", "image": {"src": "/imgs/users/5.png"}}, {"title": "<PERSON>", "label": "Startup Founder", "description": "From idea to launch in 3 days! ShipAny's templates and deployment tools made it possible to test our AI business concept incredibly fast.", "image": {"src": "/imgs/users/6.png"}}]}, "faq": {"name": "faq", "label": "FAQ", "title": "Frequently Asked Questions About ShipAny", "description": "Have another question? Contact us on Discord or by email.", "items": [{"title": "What exactly is <PERSON><PERSON><PERSON> and how does it work?", "description": "ShipAny is a comprehensive NextJS boilerplate designed specifically for building AI SaaS startups. It provides ready-to-use templates, infrastructure setup, and deployment tools that help you launch your AI business in hours instead of days."}, {"title": "Do I need advanced technical skills to use ShipAny?", "description": "While basic programming knowledge is helpful, ShipAny is designed to be developer-friendly. Our templates and documentation make it easy to get started, even if you're not an expert in AI or cloud infrastructure."}, {"title": "What types of AI SaaS can I build with ShipAny?", "description": "ShipAny supports a wide range of AI applications, from content generation to data analysis tools. Our templates cover popular use cases like AI chatbots, content generators, image processing apps, and more."}, {"title": "How long does it typically take to launch with ShipAny?", "description": "With ShipAny, you can have a working prototype in hours and a production-ready application in hours. Our one-click deployment and pre-configured infrastructure significantly reduce the traditional months-long development cycle."}, {"title": "What's included in the ShipAny infrastructure?", "description": "ShipAny provides a complete infrastructure stack including authentication, database setup, API integration, payment processing, and scalable cloud deployment. Everything is pre-configured following industry best practices."}, {"title": "Can I customize the templates to match my brand?", "description": "Absolutely! All ShipAny templates are fully customizable. You can modify the design, features, and functionality to match your brand identity and specific business requirements while maintaining the robust underlying infrastructure."}]}, "cta": {"name": "cta", "title": "Ship your first AI SaaS Startup", "description": "Start from here, ship with ShipAny.", "buttons": [{"title": "Get ShipAny", "url": "https://shipany.ai", "target": "_blank", "icon": "GoArrowUpRight"}, {"title": "Read Document", "url": "https://docs.shipany.ai", "target": "_blank", "variant": "outline"}]}, "footer": {"name": "footer", "brand": {"title": "ShipAny", "description": "ShipAny is a NextJS boilerplate for building AI SaaS startups. Ship Fast with a variety of templates and components.", "logo": {"src": "/logo.png", "alt": "ShipAny"}, "url": "/"}, "copyright": "© 2025 • ShipAny All rights reserved.", "nav": {"items": [{"title": "About", "children": [{"title": "Features", "url": "/#feature", "target": "_self"}, {"title": "Showcases", "url": "/#showcase", "target": "_self"}, {"title": "Pricing", "url": "/#pricing", "target": "_self"}]}, {"title": "Resources", "children": [{"title": "Documents", "url": "https://docs.shipany.ai", "target": "_blank"}, {"title": "Components", "url": "https://shipany.ai/components", "target": "_blank"}, {"title": "Templates", "url": "https://shipany.ai/templates", "target": "_blank"}]}, {"title": "Friends", "children": [{"title": "ThinkAny", "url": "https://thinkany.ai", "target": "_blank"}, {"title": "HeyBeauty", "url": "https://heybeauty.ai", "target": "_blank"}, {"title": "<PERSON><PERSON>", "url": "https://pagen.so", "target": "_blank"}]}]}, "social": {"items": [{"title": "X", "icon": "RiTwitterXFill", "url": "https://x.com/shipanyai", "target": "_blank"}, {"title": "<PERSON><PERSON><PERSON>", "icon": "RiGithubFill", "url": "https://github.com/shipanyai", "target": "_blank"}, {"title": "Discord", "icon": "RiDiscordFill", "url": "https://discord.gg/HQNnrzjZQS", "target": "_blank"}, {"title": "Email", "icon": "RiMailLine", "url": "mailto:<EMAIL>", "target": "_self"}]}, "agreement": {"items": [{"title": "Privacy Policy", "url": "/privacy-policy"}, {"title": "Terms of Service", "url": "/terms-of-service"}]}}}