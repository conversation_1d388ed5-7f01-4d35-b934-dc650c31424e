# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

```bash
# Install dependencies
pnpm install

# Development server
pnpm dev

# Build
pnpm build

# Start production server
pnpm start

# Lint
pnpm lint

# Bundle analysis
pnpm analyze

# Cloudflare deployment
pnpm cf:build
pnpm cf:preview
pnpm cf:deploy
```

## Architecture Overview

This is a Next.js 14 TypeScript application with App Router for AI SaaS generation. Built on ShipAny template.

### Core Technologies
- **Framework**: Next.js 14 with App Router
- **Styling**: Tailwind CSS + Shadcn UI components
- **Authentication**: NextAuth (Google, GitHub, One Tap)
- **Database**: Supabase
- **Payment**: Stripe
- **Internationalization**: next-intl (English/Chinese)
- **AI SDK**: AI SDK with multiple providers (OpenAI, OpenRouter, etc.)

### Key Architecture Patterns

**Multi-tenant Structure**:
- `app/[locale]/` - Internationalized pages
- `app/(admin)/` - Admin dashboard
- `app/(default)/` - Main application
- `app/api/` - API routes

**AI Generation Service**:
- `aisdk/` - Core AI SDK integration
- `aisdk/kling/` - Kling AI provider for image/video generation
- `aisdk/generate-video/` - Video generation workflows
- `aisdk/provider/` - Abstracted AI provider interface

**Business Logic Separation**:
- `models/` - Data models and database operations
- `services/` - Business logic layer
- `components/` - UI components with block-based architecture

### Data Flow

**Authentication**: NextAuth → Supabase user creation → Session management
**Payments**: Stripe checkout → Webhook handling → Credit system
**AI Generation**: User request → Credit validation → AI provider call → Result storage

### Component Architecture

**Block System**: Reusable layout blocks in `components/blocks/` for landing page sections
**UI Components**: Shadcn-based components in `components/ui/`
**Console Components**: Dashboard-specific components in `components/console/` and `components/dashboard/`

### Internationalization

- Page-specific translations: `i18n/pages/landing/`
- Global messages: `i18n/messages/`
- Routing handled via `i18n/routing.ts`

### Environment Configuration

Key environment variables in `.env.example`:
- Supabase configuration
- NextAuth providers (Google, GitHub)
- Stripe payment configuration
- AWS S3 storage settings
- Analytics integrations

### Development Notes

- Use functional React components with TypeScript
- Follow the established file structure patterns
- Internationalization is required for all user-facing text
- Credit system validates AI generation requests
- Admin users specified via `ADMIN_EMAILS` environment variable