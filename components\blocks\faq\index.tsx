import { Badge } from "@/components/ui/badge";
import { Section as SectionType } from "@/types/blocks/section";

export default function FAQ({ section }: { section: SectionType }) {
  if (section.disabled) {
    return null;
  }

  return (
    <section id={section.name} className="py-32">
      <div className="container">
        <div className="bg-black/20 backdrop-blur-sm rounded-lg p-8">
          <div className="text-center">
            {section.label && (
              <Badge className="text-xs font-medium bg-white/20 text-white border-white/30">{section.label}</Badge>
            )}
            <h2 className="mt-4 text-4xl font-semibold text-white">{section.title}</h2>
            <p className="mt-6 font-medium text-gray-300">
              {section.description}
            </p>
          </div>
          <div className="mx-auto mt-14 grid gap-8 md:grid-cols-2 md:gap-12">
            {section.items?.map((item, index) => (
              <div key={index} className="flex gap-4">
                <span className="flex size-6 shrink-0 items-center justify-center rounded-sm border border-white/30 font-mono text-xs text-white">
                  {index + 1}
                </span>
                <div>
                  <div className="mb-2 flex items-center justify-between">
                    <h3 className="font-semibold text-white">{item.title}</h3>
                  </div>
                  <p className="text-md text-gray-300">
                    {item.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
