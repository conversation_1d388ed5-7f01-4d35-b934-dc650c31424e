import AIGenerator from "@/components/blocks/ai-generator";
import CTA from "@/components/blocks/cta";
import FAQ from "@/components/blocks/faq";
import Feature from "@/components/blocks/feature";
import Feature2 from "@/components/blocks/feature2";
import Gallery from "@/components/blocks/gallery";
import Hero from "@/components/blocks/hero";
import Pricing from "@/components/blocks/pricing";
import Stats from "@/components/blocks/stats";
import Testimonial from "@/components/blocks/testimonial";
import { getLandingPage } from "@/services/page";
import dynamic from "next/dynamic";

const BackgroundVideo = dynamic(() => import("@/components/ui/background-video"), {
  ssr: true,
});

export async function generateMetadata({
  params: { locale },
}: {
  params: { locale: string };
}) {
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}`;
  }

  return {
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function LandingPage({
  params: { locale },
}: {
  params: { locale: string };
}) {
  const page = await getLandingPage(locale);

  return (
    <>
      {/* 背景视频 */}
      <BackgroundVideo
        videoSrc="/imgs/background/background.webm"
      />

      {/* 页面内容 */}
      {page.hero && <Hero hero={page.hero} />}
      {page.ai_generator && <AIGenerator generator={page.ai_generator} />}
      {page.gallery && <Gallery gallery={page.gallery} />}
      {page.feature && <Feature section={page.feature} />}
      {page.introduce && <Feature2 section={page.introduce} />}
      {page.stats && <Stats section={page.stats} />}
      {page.testimonial && <Testimonial section={page.testimonial} />}
      {page.faq && <FAQ section={page.faq} />}
      {page.pricing && <Pricing pricing={page.pricing} />}
      {page.cta && <CTA section={page.cta} />}
    </>
  );
}
