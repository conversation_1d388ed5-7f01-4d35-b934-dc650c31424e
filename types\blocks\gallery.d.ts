export interface ImageData {
  id: string;
  src: string;
  alt: string;
  width: number;
  height: number;
  aspectRatio: number;
  style?: string;
  color?: string;
  lighting?: string;
  composition?: string;
  prompt?: string;
  tags?: string[];
}

export interface GalleryTranslations {
  title?: string;
  subtitle?: string;
  loading?: string;
  error?: string;
  retry?: string;
  close?: string;
  image_details?: string;
  style?: string;
  color?: string;
  lighting?: string;
  composition?: string;
  prompt?: string;
  no_data?: string;
}

export interface GallerySection {
  title?: string;
  subtitle?: string;
  images?: ImageData[];
  translations?: GalleryTranslations;
}